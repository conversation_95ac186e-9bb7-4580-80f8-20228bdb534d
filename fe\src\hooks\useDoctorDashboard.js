import { useState, useEffect, useCallback } from "react";
import doctorDashboardService from "../services/doctorDashboardService";
import { fetchBloodInventory } from "../services/bloodInventoryService";
import { bloodRequestService } from "../services/bloodRequestService";
import authService from "../services/authService";
import { DOCTOR_TYPES } from "../services/mockData";

/**
 * Custom hook for Doctor Dashboard data management
 */
const useDoctorDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dashboardData, setDashboardData] = useState({
    statistics: {
      totalRequests: 0,
      pendingRequests: 0,
      approvedRequests: 0,
      completedRequests: 0,
      urgentNotifications: 0,
    },
    bloodInventory: [],
    recentRequests: [],
    notifications: [],
  });

  const currentUser = authService.getCurrentUser();
  const isBloodDepartment =
    currentUser?.doctorType === DOCTOR_TYPES.BLOOD_DEPARTMENT;

  /**
   * Load dashboard statistics
   */
  const loadDashboardStats = useCallback(async () => {
    try {
      // For blood department doctors, get all blood requests
      // For regular doctors, get only their own requests
      let requestsResponse;

      if (isBloodDepartment) {
        // Get all blood requests for hematology department
        requestsResponse = await bloodRequestService.getAllBloodRequests();
      } else {
        const doctorId = currentUser?.id;
        if (!doctorId) {
          console.warn("No doctor ID found");
          return;
        }
        requestsResponse = await bloodRequestService.getBloodRequestsByDoctor(
          doctorId
        );
      }

      if (requestsResponse && requestsResponse.success) {
        const requests = requestsResponse.data || [];

        // Process requests according to new API format
        const processedRequests = requests.map((request) => ({
          ...request,
          // Ensure consistent field names
          requestID: request.requestId || request.requestID,
          userID: request.userId || request.userID,
          patientId: request.patientId,
          patientName: request.patientName,
          bloodGroup: request.bloodGroup,
          rhType: request.rhType,
          componentId: request.componentId,
          quantity: request.quantity,
          status: request.status,
          createdTime: request.createdTime,
          // Create combined blood type for display
          bloodTypeDisplay:
            request.bloodGroup && request.rhType
              ? `${request.bloodGroup}${
                  request.rhType === "Positive" ||
                  request.rhType === "Rh+" ||
                  request.rhType === "+"
                    ? "+"
                    : "-"
                }`
              : "",
        }));

        const statistics = {
          totalRequests: processedRequests.length,
          pendingRequests: processedRequests.filter(
            (r) => parseInt(r.status) === 0
          ).length,
          approvedRequests: processedRequests.filter(
            (r) => parseInt(r.status) === 1
          ).length,
          completedRequests: processedRequests.filter(
            (r) => parseInt(r.status) === 2
          ).length,
          urgentNotifications: 0, // Will be updated from notifications
        };

        setDashboardData((prev) => ({
          ...prev,
          statistics,
          recentRequests: processedRequests
            .sort((a, b) => new Date(b.createdTime) - new Date(a.createdTime))
            .slice(0, 5), // Get 5 most recent
        }));
      } else {
        console.warn("Failed to load blood requests:", requestsResponse);
        // Set default statistics if API fails
        setDashboardData((prev) => ({
          ...prev,
          statistics: {
            totalRequests: 0,
            pendingRequests: 0,
            approvedRequests: 0,
            completedRequests: 0,
            urgentNotifications: 0,
          },
          recentRequests: [],
        }));
      }
    } catch (err) {
      console.error("Error loading dashboard stats:", err);
      setError("Không thể tải thống kê dashboard");
    }
  }, [currentUser?.id, isBloodDepartment]);

  /**
   * Load blood inventory data
   */
  const loadBloodInventory = useCallback(async () => {
    try {
      const inventoryData = await fetchBloodInventory();

      if (!inventoryData || !Array.isArray(inventoryData)) {
        console.warn("Invalid inventory data:", inventoryData);
        setDashboardData((prev) => ({
          ...prev,
          bloodInventory: [],
        }));
        return;
      }

      // Transform data for charts
      const bloodGroupData = inventoryData.reduce((acc, item) => {
        // Handle different rhType formats
        let rhSymbol = "";
        if (item.rhType === "Rh+" || item.rhType === "+") {
          rhSymbol = "+";
        } else if (item.rhType === "Rh-" || item.rhType === "-") {
          rhSymbol = "-";
        }

        const bloodType = `${item.bloodGroup}${rhSymbol}`;
        const existing = acc.find((group) => group.name === bloodType);

        if (existing) {
          existing.value += item.quantity || 0;
        } else {
          acc.push({
            name: bloodType,
            value: item.quantity || 0,
            status: item.status || "normal",
          });
        }

        return acc;
      }, []);

      setDashboardData((prev) => ({
        ...prev,
        bloodInventory: bloodGroupData,
      }));
    } catch (err) {
      console.error("Error loading blood inventory:", err);
      setError("Không thể tải dữ liệu kho máu");
    }
  }, []);

  /**
   * Load notifications
   */
  const loadNotifications = useCallback(async () => {
    try {
      // Generate notifications based on blood inventory and requests status
      const notifications = [];
      let urgentCount = 0;

      // Check blood inventory for low stock notifications
      if (
        dashboardData.bloodInventory &&
        dashboardData.bloodInventory.length > 0
      ) {
        dashboardData.bloodInventory.forEach((item) => {
          if (item.quantity <= 5) {
            // Low stock threshold
            const notification = {
              id: `inventory-${item.id || item.bloodType}`,
              type: item.quantity <= 2 ? "emergency" : "warning",
              title: `Thiếu máu ${item.name || item.bloodType}`,
              message: `Kho máu ${item.name || item.bloodType} chỉ còn ${
                item.quantity
              } đơn vị`,
              isRead: false,
              createdAt: new Date().toISOString(),
            };
            notifications.push(notification);
            if (
              !notification.isRead &&
              (notification.type === "emergency" ||
                notification.type === "warning")
            ) {
              urgentCount++;
            }
          }
        });
      }

      // Check for pending blood requests notifications
      if (dashboardData.statistics.pendingRequests > 0) {
        const notification = {
          id: "pending-requests",
          type: "info",
          title: "Yêu cầu máu mới",
          message: `Có ${dashboardData.statistics.pendingRequests} yêu cầu máu cần xử lý`,
          isRead: false,
          createdAt: new Date().toISOString(),
        };
        notifications.push(notification);
      }

      setDashboardData((prev) => ({
        ...prev,
        notifications,
        statistics: {
          ...prev.statistics,
          urgentNotifications: urgentCount,
        },
      }));
    } catch (err) {
      console.error("Error loading notifications:", err);
      setError("Không thể tải thông báo");
    }
  }, [dashboardData.bloodInventory, dashboardData.statistics.pendingRequests]);

  /**
   * Mark notification as read
   */
  const markNotificationAsRead = useCallback(async (notificationId) => {
    try {
      setDashboardData((prev) => ({
        ...prev,
        notifications: prev.notifications.map((notification) =>
          notification.id === notificationId
            ? { ...notification, isRead: true }
            : notification
        ),
      }));

      // Update urgent notifications count
      setDashboardData((prev) => ({
        ...prev,
        statistics: {
          ...prev.statistics,
          urgentNotifications: prev.notifications.filter(
            (n) =>
              n.type === "emergency" && !n.isRead && n.id !== notificationId
          ).length,
        },
      }));
    } catch (err) {
      console.error("Error marking notification as read:", err);
    }
  }, []);

  /**
   * Refresh all dashboard data
   */
  const refreshDashboard = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      console.log("Refreshing dashboard data...");

      // Load data sequentially to better handle errors
      await loadDashboardStats();
      await loadBloodInventory();
      await loadNotifications();

      console.log("Dashboard data loaded successfully");
    } catch (err) {
      console.error("Error refreshing dashboard:", err);
      setError(
        `Không thể tải dữ liệu dashboard: ${err.message || "Unknown error"}`
      );
    } finally {
      setLoading(false);
    }
  }, [loadDashboardStats, loadBloodInventory, loadNotifications]);

  // Load data on mount
  useEffect(() => {
    refreshDashboard();
  }, [refreshDashboard]);

  return {
    loading,
    error,
    dashboardData,
    isBloodDepartment,
    currentUser,
    refreshDashboard,
    markNotificationAsRead,
  };
};

export default useDoctorDashboard;
