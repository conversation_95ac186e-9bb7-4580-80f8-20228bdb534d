import React from "react";
import { <PERSON>, <PERSON><PERSON>, But<PERSON> } from "antd";
import { ReloadOutlined, DashboardOutlined } from "@ant-design/icons";
import DoctorLayout from "../../components/doctor/DoctorLayout";
import PageHeader from "../../components/doctor/PageHeader";
import useDoctorDashboard from "../../hooks/useDoctorDashboard";
import "../../styles/pages/DoctorDashboard.scss";
import "../../styles/components/DoctorPageHeader.scss";

import StatisticsCards from "../../components/doctor/dashboard/StatisticsCards";
import ChartsSection from "../../components/doctor/dashboard/ChartsSection";

const DoctorDashboard = () => {
  const {
    loading,
    error,
    dashboardData,
    isBloodDepartment,
    currentUser,
    refreshDashboard,
    markNotificationAsRead,
  } = useDoctorDashboard();

  // Handle loading state
  if (loading) {
    return (
      <DoctorLayout pageTitle="🏥 Dashboard Bác sĩ">
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            minHeight: "400px",
          }}
        >
          <Spin size="large" />
          <span style={{ marginLeft: 16 }}>Đang tải dữ liệu dashboard...</span>
        </div>
      </DoctorLayout>
    );
  }

  // Handle error state
  if (error) {
    return (
      <DoctorLayout pageTitle="🏥 Dashboard Bác sĩ">
        <Alert
          message="Lỗi tải dữ liệu"
          description={error}
          type="error"
          showIcon
          action={
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={refreshDashboard}
            >
              Thử lại
            </Button>
          }
          style={{ marginBottom: 24 }}
        />
      </DoctorLayout>
    );
  }

  return (
    <DoctorLayout>
      <div className="doctor-dashboard-content">
        <PageHeader
          title="Dashboard Bác sĩ"
          description={`Chào mừng ${
            currentUser?.name || "Bác sĩ"
          } - Tổng quan hoạt động và thống kê hệ thống`}
          icon={DashboardOutlined}
          actions={[
            {
              label: "Làm mới",
              icon: <ReloadOutlined />,
              onClick: refreshDashboard,
              loading: loading,
            },
          ]}
        />

        {/* Statistics Cards */}
        <StatisticsCards
          statistics={dashboardData.statistics}
          isBloodDepartment={isBloodDepartment}
        />

        {/* Charts Section */}
        <ChartsSection
          bloodGroupData={dashboardData.bloodInventory}
          recentRequests={dashboardData.recentRequests}
        />
      </div>
    </DoctorLayout>
  );
};

export default DoctorDashboard;
