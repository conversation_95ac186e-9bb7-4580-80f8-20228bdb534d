// Doctor <PERSON> Styles - Similar to Manager <PERSON>ead<PERSON>
.doctor-page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }

  .header-info {
    flex: 1;

    .header-title-section {
      display: flex;
      align-items: flex-start;
      gap: 16px;

      .header-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #1890ff, #096dd9);
        border-radius: 12px;
        color: white;
        font-size: 24px;
        flex-shrink: 0;

        @media (max-width: 768px) {
          width: 40px;
          height: 40px;
          font-size: 20px;
        }
      }

      .header-text {
        flex: 1;

        .header-title {
          margin-bottom: 8px !important;
          color: #20374e;
          font-weight: 600;
          font-size: 28px;
          line-height: 1.2;

          @media (max-width: 768px) {
            font-size: 24px;
          }
        }

        .header-description {
          color: #666;
          font-size: 16px;
          line-height: 1.5;
          margin: 0;

          @media (max-width: 768px) {
            font-size: 14px;
          }
        }
      }
    }
  }

  .header-actions {
    flex-shrink: 0;
    margin-left: 24px;

    @media (max-width: 768px) {
      margin-left: 0;
      width: 100%;
    }

    .ant-btn {
      height: 40px;
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      @media (max-width: 768px) {
        width: 100%;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .ant-space {
      @media (max-width: 768px) {
        width: 100%;
        flex-direction: column;
        
        .ant-space-item {
          width: 100%;
        }
      }
    }
  }
}

// Dark theme support
.doctor-page-header.dark {
  border-bottom-color: #303030;

  .header-info .header-title-section .header-text {
    .header-title {
      color: #fff;
    }

    .header-description {
      color: #bfbfbf;
    }
  }
}

// Compact variant
.doctor-page-header.compact {
  margin-bottom: 16px;
  padding: 12px 0;

  .header-info .header-title-section {
    gap: 12px;

    .header-icon {
      width: 40px;
      height: 40px;
      font-size: 20px;
    }

    .header-text .header-title {
      font-size: 24px;
    }
  }
}

// No border variant
.doctor-page-header.no-border {
  border-bottom: none;
}
