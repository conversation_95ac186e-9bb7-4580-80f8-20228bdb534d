import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>, But<PERSON> } from "antd";
import { ReloadOutlined } from "@ant-design/icons";
import ManagerLayout from "../../components/manager/ManagerLayout";
import WelcomeBanner from "../../components/manager/dashboard/WelcomeBanner";
import StatisticsCards from "../../components/manager/dashboard/StatisticsCards";
import ChartsSection from "../../components/manager/dashboard/ChartsSection";
import NotificationsPanel from "../../components/manager/dashboard/NotificationsPanel";
import authService from "../../services/authService";
import { getUserName } from "../../utils/userUtils";
import managerDashboardService from "../../services/managerDashboardService";
import "../../styles/base/manager-design-system.scss";

const ManagerDashboard = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dashboardData, setDashboardData] = useState({
    totalDonors: 0,
    totalRecipients: 0,
    totalBloodUnits: 0,
    totalRequests: 0,
    bloodInventory: [],
    bloodGroupData: [],
    monthlyRequestsData: [],
    notifications: [],
    recentRequests: [],
    criticalInventory: [],
    lowInventory: [],
  });

  useEffect(() => {
    const currentUser = authService.getCurrentUser();
    if (currentUser) {
      setUser(currentUser);
      loadDashboardData();
    }
  }, []);

  // Refresh dashboard data
  const refreshDashboard = () => {
    loadDashboardData();
  };

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get dashboard data from service
      const result = await managerDashboardService.getDashboardData();

      if (result.success) {
        const data = result.data;

        setDashboardData({
          totalDonors: data.totalDonors,
          totalRecipients: data.totalRecipients,
          totalBloodUnits: data.totalBloodUnits,
          totalRequests: data.totalRequests,
          bloodInventory: data.bloodInventory,
          bloodGroupData: data.bloodGroupData,
          monthlyRequestsData: data.monthlyRequestsData,
          notifications: [], // Will use default notifications from component
          recentRequests: data.recentRequests,
          criticalInventory: data.criticalInventory,
          lowInventory: data.lowInventory,
        });
      } else {
        throw new Error(result.error || "Không thể tải dữ liệu dashboard");
      }
    } catch (error) {
      console.error("Error loading dashboard data:", error);
      setError(error.message || "Có lỗi xảy ra khi tải dữ liệu dashboard");
    } finally {
      setLoading(false);
    }
  };

  // Handle loading state
  if (loading) {
    return (
      <ManagerLayout>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            minHeight: "400px",
          }}
        >
          <Spin size="large" />
          <span style={{ marginLeft: 16 }}>Đang tải dữ liệu dashboard...</span>
        </div>
      </ManagerLayout>
    );
  }

  // Handle error state
  if (error) {
    return (
      <ManagerLayout>
        <Alert
          message="Lỗi tải dữ liệu"
          description={error}
          type="error"
          showIcon
          action={
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={loadDashboardData}
            >
              Thử lại
            </Button>
          }
          style={{ marginBottom: 24 }}
        />
      </ManagerLayout>
    );
  }

  if (!user) {
    return <div>Loading...</div>;
  }

  const managerName = getUserName();

  return (
    <ManagerLayout>
      <div className="dashboard-content">
        {/* Welcome Banner */}
        <WelcomeBanner managerName={managerName} />

        {/* Refresh Button */}
        <div style={{ marginBottom: 16, textAlign: "right" }}>
          <Button
            icon={<ReloadOutlined />}
            onClick={refreshDashboard}
            loading={loading}
          >
            Làm mới dữ liệu
          </Button>
        </div>

        {/* Statistics Cards */}
        <StatisticsCards
          statistics={{
            totalBloodUnits: dashboardData.totalBloodUnits,
            totalDonors: dashboardData.totalDonors,
            totalRequests: dashboardData.totalRequests,
          }}
        />

        {/* Charts Section */}
        <ChartsSection
          bloodGroupData={dashboardData.bloodGroupData}
          monthlyRequestsData={dashboardData.monthlyRequestsData}
        />

        {/* Notifications Panel */}
        <NotificationsPanel notifications={dashboardData.notifications} />
      </div>
    </ManagerLayout>
  );
};

export default ManagerDashboard;
